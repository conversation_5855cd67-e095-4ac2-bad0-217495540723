package com.ideal.envc.model.dto;

import com.ideal.envc.model.enums.FileComparisonStrategy;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 文件比较请求DTO
 *
 * <AUTHOR>
 */
public class FileComparisonRequestDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 源内容字符串（基线）
     */
    @NotBlank(message = "源内容不能为空")
    private String sourceContent;

    /**
     * 目标内容字符串
     */
    @NotBlank(message = "目标内容不能为空")
    private String targetContent;

    /**
     * 基线服务器名称
     */
    private String baselineServer;

    /**
     * 基线设备IP
     */
    private String baseServerIp;

    /**
     * 目标服务器名称
     */
    private String targetServer;

    /**
     * 目标设备IP
     */
    private String targetServerIp;

    /**
     * 比较描述
     */
    private String description;

    /**
     * 流程ID
     */
    private Long flowId;
    /**
     * 文件比较策略
     * 默认为MD5_ONLY（仅MD5比较）
     */
    private FileComparisonStrategy comparisonStrategy = FileComparisonStrategy.COMPREHENSIVE;


    /**
     * 源路径
     */
    private String sourcePath;

    /**
     * 路径
     */
    private String path;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 源设备ID
     */
    private Long sourceComputerId;

    /**
     * 目标设备ID
     */
    private Long targetComputerId;

    /**
     * 目标中心名称
     */
    private String targetCenterName;

    /**
     * 源中心名称
     */
    private String sourceCenterName;

    /**
     * 业务系统名称
     */
    private String businessSystemName;


    public String getSourceContent() {
        return sourceContent;
    }

    public void setSourceContent(String sourceContent) {
        this.sourceContent = sourceContent;
    }

    public String getTargetContent() {
        return targetContent;
    }

    public void setTargetContent(String targetContent) {
        this.targetContent = targetContent;
    }

    public String getBaselineServer() {
        return baselineServer;
    }

    public void setBaselineServer(String baselineServer) {
        this.baselineServer = baselineServer;
    }

    public String getTargetServer() {
        return targetServer;
    }

    public void setTargetServer(String targetServer) {
        this.targetServer = targetServer;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBaseServerIp() {
        return baseServerIp;
    }

    public void setBaseServerIp(String baseServerIp) {
        this.baseServerIp = baseServerIp;
    }

    public String getTargetServerIp() {
        return targetServerIp;
    }

    public void setTargetServerIp(String targetServerIp) {
        this.targetServerIp = targetServerIp;
    }

    public FileComparisonStrategy getComparisonStrategy() {
        return comparisonStrategy;
    }

    public void setComparisonStrategy(FileComparisonStrategy comparisonStrategy) {
        this.comparisonStrategy = comparisonStrategy;
    }

    @Override
    public String toString() {
        return "FileComparisonRequestDto{" +
                "sourceContent='" + sourceContent + '\'' +
                ", targetContent='" + targetContent + '\'' +
                ", baselineServer='" + baselineServer + '\'' +
                ", baseServerIp='" + baseServerIp + '\'' +
                ", targetServer='" + targetServer + '\'' +
                ", targetServerIp='" + targetServerIp + '\'' +
                ", description='" + description + '\'' +
                ", flowId=" + flowId +
                ", comparisonStrategy=" + comparisonStrategy +
                ", sourcePath='" + sourcePath + '\'' +
                ", path='" + path + '\'' +
                ", businessSystemId=" + businessSystemId +
                ", sourceComputerId=" + sourceComputerId +
                ", targetComputerId=" + targetComputerId +
                ", targetCenterName='" + targetCenterName + '\'' +
                ", sourceCenterName='" + sourceCenterName + '\'' +
                ", businessSystemName='" + businessSystemName + '\'' +
                '}';
    }

    public Long getFlowId() {
        return flowId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }


    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public Long getSourceComputerId() {
        return sourceComputerId;
    }

    public void setSourceComputerId(Long sourceComputerId) {
        this.sourceComputerId = sourceComputerId;
    }

    public Long getTargetComputerId() {
        return targetComputerId;
    }

    public void setTargetComputerId(Long targetComputerId) {
        this.targetComputerId = targetComputerId;
    }

    public String getTargetCenterName() {
        return targetCenterName;
    }

    public void setTargetCenterName(String targetCenterName) {
        this.targetCenterName = targetCenterName;
    }

    public String getSourceCenterName() {
        return sourceCenterName;
    }

    public void setSourceCenterName(String sourceCenterName) {
        this.sourceCenterName = sourceCenterName;
    }

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }
}
