package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.HtmlComparisonRequestDto;
import com.ideal.envc.model.dto.HtmlComparisonResultDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IHtmlComparisonService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * HTML比对控制器测试类
 * 测试统一响应码的使用
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class HtmlComparisonControllerTest {

    @Mock
    private IHtmlComparisonService htmlComparisonService;

    @Mock
    private UserinfoComponent userinfoComponent;

    @InjectMocks
    private HtmlComparisonController htmlComparisonController;

    private UserDto userDto;
    private HtmlComparisonRequestDto request;
    private HtmlComparisonResultDto result;

    @BeforeEach
    void setUp() {
        // 初始化用户信息
        userDto = new UserDto();
        userDto.setFullName("测试用户");
        userDto.setId(1L);

        // 初始化请求参数
        request = new HtmlComparisonRequestDto();
        request.setFlowId(12345L);
        request.setBaselineServer("基线服务器");
        request.setTargetServer("目标服务器");
        request.setDescription("测试比对");

        // 初始化比对结果
        result = new HtmlComparisonResultDto();
        result.setTotalHtmlRows(100);
        result.setTotalSourceFiles(50);
        result.setTotalTargetFiles(48);
        result.setConsistentCount(45);
        result.setInconsistentCount(3);
        result.setMissingCount(2);
        result.setExtraCount(0);
        result.setConsistentRate(new BigDecimal(90));
        result.setInconsistentRate(new BigDecimal(6));
        result.setMissingRate(new BigDecimal(4));
        result.setExtraRate(new BigDecimal(0));
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 成功场景")
    void testParseHtmlComparison_Success() throws Exception {
        // 准备mock
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(result);

        // 执行测试
        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), response.getMessage());
        assertNotNull(response.getData());
        assertEquals(result, response.getData());

        // 验证方法调用
        verify(userinfoComponent, times(1)).getUser();
        verify(htmlComparisonService, times(1)).parseHtmlComparison(any(HtmlComparisonRequestDto.class));
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 流程ID为空异常")
    void testParseHtmlComparison_FlowIdEmpty() throws Exception {
        // 准备mock
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("流程ID不能为空"));

        // 执行测试
        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.FLOW_ID_EMPTY.getCode(), response.getCode());
        assertEquals("流程ID不能为空", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 流程结果不存在异常")
    void testParseHtmlComparison_FlowResultNotFound() throws Exception {
        // 准备mock
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("未查询到对应的流程结果数据"));

        // 执行测试
        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getCode(), response.getCode());
        assertEquals("未查询到对应的流程结果数据", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 系统异常")
    void testParseHtmlComparison_SystemError() throws Exception {
        // 准备mock
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new RuntimeException("系统内部错误"));

        // 执行测试
        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), response.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试快速解析HTML比对内容 - 成功场景")
    void testQuickParseHtmlComparison_Success() throws Exception {
        // 准备mock
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(result);

        // 执行测试
        R<String> response = htmlComparisonController.quickParseHtmlComparison(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), response.getMessage());
        assertNotNull(response.getData());
        assertTrue(response.getData().contains("HTML比对解析完成"));
        assertTrue(response.getData().contains("基线文件：50个"));
        assertTrue(response.getData().contains("一致：45个（90.0%）"));
    }

    @Test
    @DisplayName("测试获取比对建议 - 成功场景")
    void testGetComparisonAdvice_Success() throws Exception {
        // 准备mock
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(result);

        // 执行测试
        R<String> response = htmlComparisonController.getComparisonAdvice(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), response.getMessage());
        assertNotNull(response.getData());
        assertTrue(response.getData().contains("差异率"));
        assertTrue(response.getData().contains("详细统计"));
    }

    @Test
    @DisplayName("测试导出HTML比对结果 - 成功场景")
    void testExportHtmlComparison_Success() throws Exception {
        // 准备mock
        MockHttpServletResponse response = new MockHttpServletResponse();
        when(userinfoComponent.getUser()).thenReturn(userDto);
        doNothing().when(htmlComparisonService).exportHtmlComparisonResult(any(), any());

        // 执行测试 - 不应该抛出异常
        assertDoesNotThrow(() -> {
            htmlComparisonController.exportHtmlComparison(request, response);
        });

        // 验证方法调用
        verify(userinfoComponent, times(1)).getUser();
        verify(htmlComparisonService, times(1)).exportHtmlComparisonResult(any(), any());
    }

    @Test
    @DisplayName("测试导出HTML比对结果 - 业务异常")
    void testExportHtmlComparison_BusinessException() throws Exception {
        // 准备mock
        MockHttpServletResponse response = new MockHttpServletResponse();
        when(userinfoComponent.getUser()).thenReturn(userDto);
        doThrow(new ContrastBusinessException("Excel导出失败"))
                .when(htmlComparisonService).exportHtmlComparisonResult(any(), any());

        // 执行测试
        htmlComparisonController.exportHtmlComparison(request, response);

        // 验证响应状态
        assertEquals(500, response.getStatus());
        assertEquals("application/json;charset=utf-8", response.getContentType());
        
        String responseContent = response.getContentAsString();
        assertTrue(responseContent.contains(ResponseCodeEnum.EXCEL_EXPORT_FAIL.getCode()));
        assertTrue(responseContent.contains("Excel导出失败"));
    }

    @Test
    @DisplayName("测试统一响应码枚举的完整性")
    void testResponseCodeEnumCompleteness() {
        // 验证比对解析相关响应码
        assertNotNull(ResponseCodeEnum.COMPARISON_PARSE_FAIL);
        assertEquals("130600", ResponseCodeEnum.COMPARISON_PARSE_FAIL.getCode());
        assertEquals("比对解析失败", ResponseCodeEnum.COMPARISON_PARSE_FAIL.getDesc());

        assertNotNull(ResponseCodeEnum.FLOW_ID_EMPTY);
        assertEquals("130602", ResponseCodeEnum.FLOW_ID_EMPTY.getCode());
        assertEquals("流程ID不能为空", ResponseCodeEnum.FLOW_ID_EMPTY.getDesc());

        assertNotNull(ResponseCodeEnum.FLOW_RESULT_NOT_FOUND);
        assertEquals("130603", ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getCode());
        assertEquals("流程结果不存在", ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getDesc());

        // 验证导出相关响应码
        assertNotNull(ResponseCodeEnum.EXPORT_FAIL);
        assertEquals("130700", ResponseCodeEnum.EXPORT_FAIL.getCode());
        assertEquals("导出失败", ResponseCodeEnum.EXPORT_FAIL.getDesc());

        assertNotNull(ResponseCodeEnum.EXCEL_EXPORT_FAIL);
        assertEquals("130701", ResponseCodeEnum.EXCEL_EXPORT_FAIL.getCode());
        assertEquals("Excel导出失败", ResponseCodeEnum.EXCEL_EXPORT_FAIL.getDesc());
    }

    @Test
    @DisplayName("测试业务异常响应方法的完整性")
    void testBusinessExceptionResponseCompleteness() throws Exception {
        // 测试各种业务异常的响应码映射
        String[] testMessages = {
                "流程ID不能为空",
                "未查询到对应的流程结果数据",
                "未查询到对应设备相关信息",
                "流程结果内容为空",
                "HTML内容为空",
                "内容格式不正确",
                "HTML解析失败",
                "JSON解析失败",
                "其他解析失败"
        };

        String[] expectedCodes = {
                ResponseCodeEnum.FLOW_ID_EMPTY.getCode(),
                ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getCode(),
                ResponseCodeEnum.FLOW_DETAIL_NOT_FOUND.getCode(),
                ResponseCodeEnum.FLOW_CONTENT_EMPTY.getCode(),
                ResponseCodeEnum.HTML_CONTENT_EMPTY.getCode(),
                ResponseCodeEnum.CONTENT_PARSE_FAIL.getCode(),
                ResponseCodeEnum.HTML_PARSE_FAIL.getCode(),
                ResponseCodeEnum.JSON_PARSE_FAIL.getCode(),
                ResponseCodeEnum.COMPARISON_PARSE_FAIL.getCode()
        };

        for (int i = 0; i < testMessages.length; i++) {
            when(userinfoComponent.getUser()).thenReturn(userDto);
            when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                    .thenThrow(new ContrastBusinessException(testMessages[i]));

            R<String> response = htmlComparisonController.quickParseHtmlComparison(request);

            assertEquals(expectedCodes[i], response.getCode(), 
                        "消息 '" + testMessages[i] + "' 应该映射到响应码 " + expectedCodes[i]);
            assertEquals(testMessages[i], response.getMessage());
        }
    }
}
