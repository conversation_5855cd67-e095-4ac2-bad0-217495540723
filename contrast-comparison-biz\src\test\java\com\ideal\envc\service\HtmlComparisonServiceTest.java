package com.ideal.envc.service;

import com.ideal.envc.model.dto.HtmlComparisonRequestDto;
import com.ideal.envc.model.dto.HtmlComparisonResultDto;
import com.ideal.envc.service.impl.HtmlComparisonServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * HTML比对服务测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("HTML比对服务测试类")
public class HtmlComparisonServiceTest {

    @Mock
    private IFileComparisonService fileComparisonService;

    @Mock
    private com.ideal.envc.mapper.RunFlowResultMapper runFlowResultMapper;

    @Mock
    private com.ideal.envc.mapper.RunRuleMapper runRuleMapper;

    @InjectMocks
    private HtmlComparisonServiceImpl htmlComparisonService;

    /**
     * 测试通过flowId解析比对内容
     */
    @Test
    @DisplayName("测试通过flowId解析比对内容")
    public void testParseByFlowId() {
        // 使用一个测试用的flowId
        Long testFlowId = 12345L;

        HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
        request.setFlowId(testFlowId);
        request.setBaselineServer("基线服务器");
        request.setTargetServer("目标服务器");
        request.setDescription("flowId解析测试");

        try {
            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);

            System.out.println("=== flowId解析测试结果 ===");
            System.out.println("flowId：" + testFlowId);
            System.out.println("基线服务器：" + result.getBaselineServer());
            System.out.println("目标服务器：" + result.getTargetServer());
            System.out.println("HTML行数：" + result.getTotalHtmlRows());
            System.out.println("基线文件总数：" + result.getTotalSourceFiles());
            System.out.println("目标文件总数：" + result.getTotalTargetFiles());
            System.out.println("一致文件：" + result.getConsistentCount() + "个（" + result.getConsistentRate() + "%）");
            System.out.println("不一致文件：" + result.getInconsistentCount() + "个（" + result.getInconsistentRate() + "%）");
            System.out.println("缺失文件：" + result.getMissingCount() + "个（" + result.getMissingRate() + "%）");
            System.out.println("多出文件：" + result.getExtraCount() + "个（" + result.getExtraRate() + "%）");

        } catch (Exception e) {
            System.err.println("flowId解析测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试JSON格式解析
     */
    @Test
    public void testParseJsonFormat() {
        String jsonContent = "{\n" +
            "    \"sourceContent\": \"COPYRIGHT (size: 3.17 KB, permissions: -rwxrwxr-x, MD5: a762796b2a8989b8952b653a178607a1)\\nLICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: 98f46ab6481d87c4d77e0e91a6dbc15f)\\nREADME (size: 46.00 B, permissions: -rw-r--r--, MD5: 0f1123976b959ac5e8b89eb8c245c4bd)\\nrelease (size: 424.00 B, permissions: -rwxrwxr-x, MD5: 994c1876d190830b199d08d4b90430a7)\",\n" +
            "    \"targetContent\": \"COPYRIGHT (size: 3.17 KB, permissions: -rw-r--r--, MD5: a762796b2a8989b8952b653a178607a1)\\nLICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: 98f46ab6481d87c4d77e0e91a6dbc15f)\\nREADME (size: 46.00 B, permissions: -rw-r--r--, MD5: 0f1123976b959ac5e8b89eb8c245c4bd)\\nrelease (size: 424.00 B, permissions: -rw-r--r--, MD5: 994c1876d190830b199d08d4b90430a7)\"\n" +
            "}";

        HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
        request.setHtmlContent(jsonContent);
        request.setBaselineServer("基线服务器");
        request.setTargetServer("目标服务器");
        request.setDescription("JSON格式测试");

        try {
            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);
            
            System.out.println("=== JSON格式解析测试结果 ===");
            System.out.println("基线服务器：" + result.getBaselineServer());
            System.out.println("目标服务器：" + result.getTargetServer());
            System.out.println("HTML行数：" + result.getTotalHtmlRows());
            System.out.println("基线文件总数：" + result.getTotalSourceFiles());
            System.out.println("目标文件总数：" + result.getTotalTargetFiles());
            System.out.println("一致文件：" + result.getConsistentCount() + "个（" + result.getConsistentRate() + "%）");
            System.out.println("不一致文件：" + result.getInconsistentCount() + "个（" + result.getInconsistentRate() + "%）");
            System.out.println("缺失文件：" + result.getMissingCount() + "个（" + result.getMissingRate() + "%）");
            System.out.println("多出文件：" + result.getExtraCount() + "个（" + result.getExtraRate() + "%）");
            
            // 显示详细的文件信息
            if (result.getInconsistentFiles() != null && !result.getInconsistentFiles().isEmpty()) {
                System.out.println("\n=== 不一致文件详情 ===");
                result.getInconsistentFiles().forEach(file -> {
                    System.out.println("文件：" + file.getFilePath());
                    System.out.println("状态：" + file.getStatus());
                    System.out.println("备注：" + file.getRemark());
                    System.out.println("---");
                });
            }
            
        } catch (Exception e) {
            System.err.println("JSON格式解析测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试纯文本格式解析
     */
    @Test
    public void testParseTextFormat() {
        String textContent = 
            "COPYRIGHT (size: 3.17 KB, permissions: -rwxrwxr-x, MD5: a762796b2a8989b8952b653a178607a1)\n" +
            "LICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: 98f46ab6481d87c4d77e0e91a6dbc15f)\n" +
            "README (size: 46.00 B, permissions: -rw-r--r--, MD5: 0f1123976b959ac5e8b89eb8c245c4bd)\n" +
            "release (size: 424.00 B, permissions: -rwxrwxr-x, MD5: 994c1876d190830b199d08d4b90430a7)";

        HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
        request.setHtmlContent(textContent);
        request.setBaselineServer("基线服务器");
        request.setTargetServer("目标服务器");
        request.setDescription("纯文本格式测试");

        try {
            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);
            
            System.out.println("=== 纯文本格式解析测试结果 ===");
            System.out.println("基线服务器：" + result.getBaselineServer());
            System.out.println("目标服务器：" + result.getTargetServer());
            System.out.println("HTML行数：" + result.getTotalHtmlRows());
            System.out.println("基线文件总数：" + result.getTotalSourceFiles());
            System.out.println("目标文件总数：" + result.getTotalTargetFiles());
            System.out.println("一致文件：" + result.getConsistentCount() + "个（" + result.getConsistentRate() + "%）");
            System.out.println("不一致文件：" + result.getInconsistentCount() + "个（" + result.getInconsistentRate() + "%）");
            System.out.println("缺失文件：" + result.getMissingCount() + "个（" + result.getMissingRate() + "%）");
            System.out.println("多出文件：" + result.getExtraCount() + "个（" + result.getExtraRate() + "%）");
            
        } catch (Exception e) {
            System.err.println("纯文本格式解析测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试错误处理
     */
    @Test
    public void testErrorHandling() {
        System.out.println("=== 错误处理测试 ===");

        // 测试空flowId
        try {
            HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
            request.setFlowId(null);
            htmlComparisonService.parseHtmlComparison(request);
        } catch (Exception e) {
            System.out.println("空flowId错误处理正常：" + e.getMessage());
        }

        // 测试不存在的flowId
        try {
            HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
            request.setFlowId(999999L); // 假设这个flowId不存在
            request.setBaselineServer("基线服务器");
            request.setTargetServer("目标服务器");
            htmlComparisonService.parseHtmlComparison(request);
        } catch (Exception e) {
            System.out.println("不存在flowId错误处理正常：" + e.getMessage());
        }
    }
}
