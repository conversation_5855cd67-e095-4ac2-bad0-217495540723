<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.ResultMonitorMapper">

    <resultMap type="com.ideal.envc.model.bean.ResultMonitorBean" id="ResultMonitorResult">
        <result property="id" column="id"/>
        <result property="from" column="ifrom"/>
        <result property="businessSystemId" column="business_system_id"/>
        <result property="businessSystemName" column="business_system_name"/>
        <result property="model" column="model"/>
        <result property="createTime" column="create_time"/>
        <result property="sourceComputerIp" column="source_computer_ip"/>
        <result property="targetComputerIp" column="target_computer_ip"/>
        <result property="path" column="path"/>
        <result property="sourcePath" column="sourcePath"/>
        <result property="elapsedTime" column="elapsed_time"/>
        <result property="result" column="result"/>
        <result property="state" column="state"/>
        <result property="runRuleId" column="runRuleId"/>
        <result property="flowId" column="flowId"/>
        <result property="type" column="itype"/>
    </resultMap>

    <sql id="selectResultMonitor">
        SELECT
            rr.iid AS id,
            ri.ifrom,
            p.ibusiness_system_id AS business_system_id,
            p.ibusiness_system_name AS business_system_name,
            rr.imodel AS model,
            rr.icreate_time AS create_time,
            rii.isource_computer_ip AS source_computer_ip,
            rii.itarget_computer_ip AS target_computer_ip,
            rr.ipath AS path,
            rr.isource_path AS sourcePath,
            rr.ielapsed_time AS elapsed_time,
            rr.iresult AS result,
            rr.istate AS state,
            rr.iid AS runRuleId,
            rf.iflowid AS flowId,
            rf.icreate_time AS create_time,
            rr.itype
        FROM
            ieai_envc_run_instance ri
            INNER JOIN ieai_envc_run_instance_info rii ON ri.iid = rii.ienvc_run_instance_id
            INNER JOIN ieai_envc_run_rule rr ON rii.iid = rr.ienvc_run_instance_info_id
            INNER JOIN ieai_envc_plan pl ON ri.ienvc_plan_id = pl.iid
            INNER JOIN ieai_envc_plan_relation pr ON pl.iid = pr.ienvc_plan_id and pr.ibusiness_system_id = rii.ibusiness_system_id
            INNER JOIN ieai_envc_project p ON pr.ibusiness_system_id = p.ibusiness_system_id
            INNER JOIN ieai_envc_system_computer_node n on p.ibusiness_system_id = n.ibusiness_system_id
                and  n.isource_computer_id = rii.isource_computer_id and n.itarget_computer_id = rii.itarget_computer_id
            LEFT JOIN ieai_envc_run_flow rf ON rf.irun_biz_id = rr.iid
    </sql>

    <select id="selectResultMonitorList" resultMap="ResultMonitorResult">
        <include refid="selectResultMonitor"/>
        <where>
            <if test="businessSystemName != null and businessSystemName != ''">
                AND p.ibusiness_system_name LIKE CONCAT('%', #{businessSystemName}, '%')
            </if>
            <if test="model != null">
                AND rr.imodel = #{model}
            </if>
            <if test="result != null">
                AND rr.iresult = #{result}
            </if>
            <if test="from != null">
                AND ri.ifrom = #{from}
            </if>
        </where>
        ORDER BY rr.icreate_time DESC
    </select>

</mapper>
